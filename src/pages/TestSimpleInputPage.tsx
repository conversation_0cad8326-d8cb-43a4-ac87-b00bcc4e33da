import { useState, useEffect } from 'react';

// Simple test form without @tanstack/react-form
export const TestSimpleInputPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    Strasse: '',
    Hausnr: '',
    PLZ: '',
    Ort: '',
  });

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Simulate data loading and form value updates
  useEffect(() => {
    if (!isLoading) {
      // Simulate existing data being loaded
      const existingData = {
        Strasse: 'Test Straße',
        Hausnr: '123',
        PLZ: '12345',
        Ort: 'Test Stadt',
      };

      // Use timeout to simulate async data loading
      const timeoutId = setTimeout(() => {
        console.log('Setting form values from useEffect in SimpleInputPage');
        setFormData(existingData);
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [isLoading]);

  const handleInputChange = (name: string, value: string) => {
    console.log(`Field ${name} changed to:`, value);
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFocus = (name: string) => {
    console.log(`Field ${name} focused`);
  };

  const handleBlur = (name: string) => {
    console.log(`Field ${name} blurred`);
  };

  const SimpleFormField = ({
    name,
    label,
    placeholder = '',
  }: {
    name: keyof typeof formData;
    label: string;
    placeholder?: string;
  }) => {
    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
        <input
          id={name}
          name={name}
          type="text"
          value={formData[name]}
          onChange={(e) => handleInputChange(name, e.target.value)}
          onBlur={() => handleBlur(name)}
          onFocus={() => handleFocus(name)}
          placeholder={placeholder}
          autoComplete="off"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Test Simple Input Page (No @tanstack/react-form)
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        This page tests input field focus behavior with regular React state.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={handleSubmit}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Test Form Fields (Regular React State)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <SimpleFormField name="Strasse" label="Straße" placeholder="Straße des Objektes" />
                </div>
                <SimpleFormField name="Hausnr" label="Hausnummer" placeholder="222" />
              </div>
              <SimpleFormField name="PLZ" label="PLZ" placeholder="99423" />
              <SimpleFormField name="Ort" label="Ort" placeholder="Weimar" />
            </div>
          </div>

          <div className="flex justify-between mt-8">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Weiter
            </button>
          </div>
        </form>
      )}
    </div>
  );
};
