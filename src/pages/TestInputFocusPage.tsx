import { useState, useEffect, useRef, useCallback } from 'react';
import { useForm, useField } from '@tanstack/react-form';

// Test form values type
interface TestFormValues {
  Strasse: string;
  Hausnr: string;
  PLZ: string;
  Ort: string;
}

export const TestInputFocusPage = () => {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading state like in the real component
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const initialValues: TestFormValues = {
    Strasse: '',
    Hausnr: '',
    PLZ: '',
    Ort: '',
  };

  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      console.log('Form submitted:', value);
    },
  });

  // Simulate data loading and form value updates like in the real component
  useEffect(() => {
    if (!isLoading) {
      // Simulate existing data being loaded
      const existingData = {
        Strasse: 'Test Straße',
        Hausnr: '123',
        PLZ: '12345',
        Ort: 'Test Stadt',
      };

      // Use timeout like in the real component
      const timeoutId = setTimeout(() => {
        console.log('Setting form values from useEffect');
        Object.entries(existingData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            console.log(`Setting ${key} to ${value}`);
            form.setFieldValue(key as keyof TestFormValues, value);
          }
        });
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [isLoading]); // Remove form from dependencies to prevent re-runs

  const TestFormField = ({
    name,
    label,
    placeholder = '',
  }: {
    name: keyof TestFormValues;
    label: string;
    placeholder?: string;
  }) => {
    const field = useField({
      form,
      name: name as any,
    });

    // Use local state for the input value to prevent focus loss
    const [localValue, setLocalValue] = useState(field.state.value ?? '');
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Update local value when field value changes (from external sources)
    useEffect(() => {
      setLocalValue(field.state.value ?? '');
    }, [field.state.value]);

    // Add debugging for focus issues
    const handleFocus = useCallback(() => {
      console.log(`Field ${name} focused`);
    }, [name]);

    const handleBlur = useCallback(() => {
      console.log(`Field ${name} blurred`);
      // Update the form field with the current local value on blur
      field.handleChange(localValue);
      field.handleBlur();
    }, [name, field, localValue]);

    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      console.log(`Field ${name} changed to:`, value);

      // Update local state immediately for responsive UI
      setLocalValue(value);

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Debounce form state updates to prevent re-renders during typing
      timeoutRef.current = setTimeout(() => {
        field.handleChange(value);
      }, 300); // 300ms debounce
    }, [name, field]);

    // Cleanup timeout on unmount
    useEffect(() => {
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    return (
      <div className="mb-4">
        <label htmlFor={field.name} className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
        <input
          id={field.name}
          name={field.name}
          type="text"
          value={localValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          placeholder={placeholder}
          autoComplete="off"
          spellCheck={false}
          autoCorrect="off"
          autoCapitalize="off"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500"
        />
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Test Input Focus Page
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        This page tests input field focus behavior with @tanstack/react-form.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Test Form Fields
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <TestFormField name="Strasse" label="Straße" placeholder="Straße des Objektes" />
                </div>
                <TestFormField name="Hausnr" label="Hausnummer" placeholder="222" />
              </div>
              <TestFormField name="PLZ" label="PLZ" placeholder="99423" />
              <TestFormField name="Ort" label="Ort" placeholder="Weimar" />
            </div>
          </div>

          <div className="flex justify-between mt-8">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </button>
            <button
              type="submit"
              disabled={form.state.isSubmitting}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting ? 'Wird gespeichert...' : 'Weiter'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};
