import { useField } from '@tanstack/react-form';
import { formatValidationError } from '../../utils/formValidation';

interface RadioFieldProps<T> {
  name: keyof T;
  label: string;
  required?: boolean;
  form: any; // Form instance from Tanstack Form
  helpText?: string;
}

export const RadioField = <T,>({
  name,
  label,
  required = false,
  form,
  helpText
}: RadioFieldProps<T>) => {
  const { state, handleChange } = useField({
    name: String(name),
    form,
  });

  // Check if field has errors
  const hasErrors = state.meta.errors.length > 0;
  const errors: (string | undefined)[] = state.meta.errors || [];

  // Format errors for better display
    const formattedErrors = errors
      .filter((error): error is string => typeof error === 'string')
      .map((error: string) => formatValidationError(error, String(name)));

  return (
    <div className="mb-4">
      <fieldset>
        <legend className="block text-sm font-medium text-gray-700 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </legend>
        <div className={`flex space-x-4 ${hasErrors ? 'p-2 border border-red-300 rounded-md bg-red-50' : ''}`}>
          <label className="flex items-center">
            <input
              type="radio"
              name={name as string}
              value="0"
              checked={state.value === '0'}
              onChange={(e) => handleChange(e.target.value)}
              className={`mr-2 h-4 w-4 ${hasErrors ? 'text-red-600 focus:ring-red-500' : 'text-green-600 focus:ring-green-500'} border-gray-300`}
              aria-invalid={hasErrors}
              aria-describedby={hasErrors ? `${String(name)}-error` : helpText ? `${String(name)}-help` : undefined}
            />
            Nein
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name={name as string}
              value="1"
              checked={state.value === '1'}
              onChange={(e) => handleChange(e.target.value)}
              className={`mr-2 h-4 w-4 ${hasErrors ? 'text-red-600 focus:ring-red-500' : 'text-green-600 focus:ring-green-500'} border-gray-300`}
              aria-invalid={hasErrors}
              aria-describedby={hasErrors ? `${String(name)}-error` : helpText ? `${String(name)}-help` : undefined}
            />
            Ja
          </label>
        </div>
      </fieldset>

      {/* Error messages */}
      {hasErrors && (
        <div id={`${String(name)}-error`} className="mt-1">
          {formattedErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}

      {/* Help text */}
      {!hasErrors && helpText && (
        <p id={`${String(name)}-help`} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}
    </div>
  );
};
